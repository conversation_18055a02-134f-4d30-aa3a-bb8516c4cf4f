"use client";

import { useEffect, useState } from 'react';

const ProcessCard = ({ processSteps }) => {
  const [visibleCards, setVisibleCards] = useState(new Set());

  useEffect(() => {
    const handleScroll = () => {
      if (typeof window === 'undefined') return;

      const windowHeight = window.innerHeight;
      const scrollY = window.scrollY;

      // Find process section
      const processSection = document.querySelector('[data-section="process"]');
      if (!processSection) return;

      const rect = processSection.getBoundingClientRect();
      const sectionTop = scrollY + rect.top;
      const scrollIntoSection = scrollY - sectionTop;

      // Each card appears every 100vh of scroll
      const newVisibleCards = new Set();
      processSteps.forEach((_, index) => {
        const cardTrigger = index * windowHeight;
        if (scrollIntoSection >= cardTrigger) {
          newVisibleCards.add(index);
        }
      });

      setVisibleCards(newVisibleCards);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [processSteps]);

  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Render all process cards */}
      {processSteps.map((step, index) => {
        const isVisible = visibleCards.has(index);

        return (
          <div
            key={step.id}
            className="absolute bg-primary rounded-2xl border border-secondary/20 shadow-lg p-6"
            style={{
              opacity: isVisible ? 1 : 0,
              transform: `scale(${isVisible ? 1 : 0.8}) translateZ(0)`,
              // All cards sit at center - no movement, just appear/disappear
              width: 'min(450px, 40vw)',
              height: 'min(280px, 25vh)',
              zIndex: 20 + index,
              transition: 'opacity 0.3s ease, transform 0.3s ease',
              boxShadow: `0px 4px 20px rgba(0, 0, 0, 0.3)`
            }}
          >
            {/* Horizontal Layout: Number on left, Text on right */}
            <div className="flex items-start gap-6 h-full">
              {/* Step Number - Left Side */}
              <div className="flex-shrink-0 w-16 h-16 bg-accent rounded-full flex items-center justify-center">
                <span className="text-primary text-2xl font-bold font-heading">
                  {step.number}
                </span>
              </div>

              {/* Text Content - Right Side */}
              <div className="flex-1 flex flex-col justify-center">
                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl mb-3">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProcessCard;
